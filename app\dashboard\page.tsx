'use client';

import { useRouter } from 'next/navigation';

export default function Dashboard() {
  const router = useRouter();

  const handleStartAssessment = () => {
    router.push('/assessment');
  };

  const handleViewAllQuestions = () => {
    router.push('/all-questions');
  };

  const handleSelectAssessment = () => {
    router.push('/select-assessment');
  };

  return (
    <div className="min-h-screen bg-[#f5f7fb]">
      {/* Header */}
      <div className="bg-white border-b border-[#eaecf0] px-8 py-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-[#313131]">Dashboard Assessment</h1>
          <p className="text-[#64707d] mt-2">Kelola dan mulai assessment psikologi Anda</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Assessment Card */}
          <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-8 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-[#e7eaff] rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-[#6475e9]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-[#313131]">Mulai Assessment</h3>
                <p className="text-[#64707d] text-sm">Big Five, RIASEC, VIA</p>
              </div>
            </div>
            <p className="text-[#64707d] mb-6">
              Mulai assessment psikologi lengkap dengan tiga fase: Big Five Personality, RIASEC Holland Codes, dan VIA Character Strengths.
            </p>
            <button
              onClick={handleStartAssessment}
              className="w-full bg-[#6475e9] text-white py-3 px-4 rounded-lg font-semibold hover:bg-[#5a6fd8] transition-colors"
            >
              Mulai Assessment
            </button>
          </div>

          {/* View All Questions Card */}
          <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-8 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-[#fef3c7] rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-[#313131]">Lihat Semua Soal</h3>
                <p className="text-[#64707d] text-sm">Preview pertanyaan</p>
              </div>
            </div>
            <p className="text-[#64707d] mb-6">
              Lihat preview semua pertanyaan dari ketiga assessment untuk memahami jenis pertanyaan yang akan dijawab.
            </p>
            <button
              onClick={handleViewAllQuestions}
              className="w-full bg-[#f59e0b] text-white py-3 px-4 rounded-lg font-semibold hover:bg-[#d97706] transition-colors"
            >
              Lihat Semua Soal
            </button>
          </div>

          {/* Select Assessment Card */}
          <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-8 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-[#dcfce7] rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-[#22c55e]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-[#313131]">Pilih Assessment</h3>
                <p className="text-[#64707d] text-sm">Kustomisasi pilihan</p>
              </div>
            </div>
            <p className="text-[#64707d] mb-6">
              Pilih assessment tertentu yang ingin dikerjakan atau kustomisasi pengaturan assessment sesuai kebutuhan.
            </p>
            <button
              onClick={handleSelectAssessment}
              className="w-full bg-[#22c55e] text-white py-3 px-4 rounded-lg font-semibold hover:bg-[#16a34a] transition-colors"
            >
              Pilih Assessment
            </button>
          </div>
        </div>

        {/* Statistics Section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-[#313131] mb-8">Statistik Assessment</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-6 text-center">
              <div className="text-3xl font-bold text-[#6475e9] mb-2">200</div>
              <div className="text-[#64707d] text-sm">Total Pertanyaan</div>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-6 text-center">
              <div className="text-3xl font-bold text-[#22c55e] mb-2">3</div>
              <div className="text-[#64707d] text-sm">Jenis Assessment</div>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-6 text-center">
              <div className="text-3xl font-bold text-[#f59e0b] mb-2">30-45</div>
              <div className="text-[#64707d] text-sm">Menit Pengerjaan</div>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-6 text-center">
              <div className="text-3xl font-bold text-[#ef4444] mb-2">24</div>
              <div className="text-[#64707d] text-sm">Kekuatan Karakter</div>
            </div>
          </div>
        </div>

        {/* Assessment Types Info */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-[#313131] mb-8">Jenis Assessment</h2>
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-8">
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-[#e7eaff] rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-2xl font-bold text-[#6475e9]">1</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-[#313131] mb-2">Big Five Personality</h3>
                  <p className="text-[#64707d] mb-4">
                    Assessment kepribadian berdasarkan lima dimensi utama: Openness, Conscientiousness, Extraversion, Agreeableness, dan Neuroticism.
                  </p>
                  <div className="flex items-center gap-4 text-sm text-[#64707d]">
                    <span>📊 44 Pertanyaan</span>
                    <span>⏱️ 10-15 Menit</span>
                    <span>🎯 5 Dimensi Kepribadian</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-8">
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-[#fef3c7] rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-2xl font-bold text-[#f59e0b]">2</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-[#313131] mb-2">RIASEC Holland Codes</h3>
                  <p className="text-[#64707d] mb-4">
                    Assessment minat karir berdasarkan teori Holland: Realistic, Investigative, Artistic, Social, Enterprising, dan Conventional.
                  </p>
                  <div className="flex items-center gap-4 text-sm text-[#64707d]">
                    <span>📊 60 Pertanyaan</span>
                    <span>⏱️ 15-20 Menit</span>
                    <span>🎯 6 Tipe Minat Karir</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-[#eaecf0] p-8">
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-[#dcfce7] rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-2xl font-bold text-[#22c55e]">3</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-[#313131] mb-2">VIA Character Strengths</h3>
                  <p className="text-[#64707d] mb-4">
                    Assessment kekuatan karakter berdasarkan 24 kekuatan karakter yang dikelompokkan dalam 6 kebajikan utama.
                  </p>
                  <div className="flex items-center gap-4 text-sm text-[#64707d]">
                    <span>📊 96 Pertanyaan</span>
                    <span>⏱️ 20-25 Menit</span>
                    <span>🎯 24 Kekuatan Karakter</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
