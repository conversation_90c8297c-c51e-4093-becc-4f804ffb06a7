'use client';

import { useAssessment } from '../../contexts/AssessmentContext';
import { assessmentTypes } from '../../data/assessmentQuestions';

export default function AssessmentSidebar() {
  const {
    currentAssessmentIndex,
    currentSectionIndex,
    setCurrentAssessmentIndex,
    setCurrentSectionIndex,
    answers,
    getProgress
  } = useAssessment();

  const progress = getProgress();

  // Get Big Five categories and their question counts
  const bigFiveAssessment = assessmentTypes[0];
  const bigFiveGrouped = bigFiveAssessment.questions.reduce((acc: any, q: any) => {
    acc[q.category] = acc[q.category] || [];
    acc[q.category].push(q);
    return acc;
  }, {});
  const bigFiveCategories = Object.keys(bigFiveGrouped);

  const handlePhaseClick = (assessmentIndex: number) => {
    setCurrentAssessmentIndex(assessmentIndex);
    setCurrentSectionIndex(0); // Reset to first section of new assessment
  };

  const handleSectionClick = (assessmentIndex: number, sectionIndex: number) => {
    setCurrentAssessmentIndex(assessmentIndex);
    setCurrentSectionIndex(sectionIndex);
  };

  const getSectionProgress = (assessmentIndex: number, sectionIndex?: number) => {
    const assessment = assessmentTypes[assessmentIndex];
    if (sectionIndex !== undefined && assessmentIndex === 0) {
      // Big Five - calculate section progress
      const category = bigFiveCategories[sectionIndex];
      const questionsInSection = bigFiveGrouped[category] || [];
      const answeredInSection = questionsInSection.filter((q: any) => answers[q.id] != null).length;
      return { answered: answeredInSection, total: questionsInSection.length };
    } else {
      // Overall assessment progress
      const questionsInAssessment = assessment.questions;
      const answeredInAssessment = questionsInAssessment.filter((q: any) => answers[q.id] != null).length;
      return { answered: answeredInAssessment, total: questionsInAssessment.length };
    }
  };

  return (
    <aside className="w-[280px] bg-white border-r border-[#eaecf0] p-6 flex flex-col gap-6">
      <h2 className="font-bold text-lg mb-4">Assessment Progress</h2>

      {/* Phase 1 - Big Five */}
      <div className={`rounded-xl p-4 mb-2 border cursor-pointer transition-all ${
        currentAssessmentIndex === 0
          ? 'bg-[#f5f7fb] border-[#e7eaff]'
          : 'bg-white border-[#eaecf0] hover:bg-[#f9fafb]'
      }`}>
        <div
          className="flex items-center justify-between mb-1"
          onClick={() => handlePhaseClick(0)}
        >
          <span className="font-semibold text-sm">Phase 1</span>
          <span className="text-xs text-[#64707d] font-medium">
            {getSectionProgress(0).answered}/{getSectionProgress(0).total}
          </span>
        </div>
        <div className="text-xs text-[#64707d] mb-2">Big Five Personality</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full mb-2">
          <div
            className="h-2 bg-[#6475e9] rounded-full transition-all"
            style={{
              width: `${Math.round((getSectionProgress(0).answered / getSectionProgress(0).total) * 100)}%`
            }}
          />
        </div>

        {/* Sub-phases for Big Five */}
        {currentAssessmentIndex === 0 && (
          <div className="flex flex-col gap-2">
            {bigFiveCategories.map((category, index) => {
              const sectionProgress = getSectionProgress(0, index);
              const isActive = currentAssessmentIndex === 0 && currentSectionIndex === index;
              const categoryNames: Record<string, string> = {
                'Openness to Experience': 'Openness to Experience',
                'Conscientiousness': 'Conscientiousness',
                'Extraversion': 'Extraversion',
                'Agreeableness': 'Agreeableness',
                'Neuroticism': 'Neuroticism'
              };

              return (
                <div
                  key={category}
                  className={`flex items-center justify-between rounded-lg px-3 py-2 cursor-pointer transition-all ${
                    isActive
                      ? 'bg-[#e7eaff]'
                      : 'hover:bg-[#f0f1f3]'
                  }`}
                  onClick={() => handleSectionClick(0, index)}
                >
                  <span className={`text-sm ${isActive ? 'text-[#6475e9] font-semibold' : 'text-[#64707d]'}`}>
                    {categoryNames[category] || category}
                  </span>
                  <span className={`rounded-full px-2 py-1 text-xs font-bold border ${
                    isActive
                      ? 'bg-white text-[#6475e9] border-[#e7eaff]'
                      : 'bg-[#f5f7fb] text-[#64707d] border-[#eaecf0]'
                  }`}>
                    {sectionProgress.answered}/{sectionProgress.total}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </div>
      {/* Phase 2 - RIASEC */}
      <div className={`rounded-xl p-4 mb-2 border cursor-pointer transition-all ${
        currentAssessmentIndex === 1
          ? 'bg-[#f5f7fb] border-[#e7eaff]'
          : 'bg-white border-[#eaecf0] hover:bg-[#f9fafb]'
      }`}>
        <div
          className="flex items-center justify-between mb-1"
          onClick={() => handlePhaseClick(1)}
        >
          <span className="font-semibold text-sm">Phase 2</span>
          <span className="text-xs text-[#64707d] font-medium">
            {getSectionProgress(1).answered}/{getSectionProgress(1).total}
          </span>
        </div>
        <div className="text-xs text-[#64707d] mb-2">RIASEC Holland Codes</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full">
          <div
            className="h-2 bg-[#6475e9] rounded-full transition-all"
            style={{
              width: `${Math.round((getSectionProgress(1).answered / getSectionProgress(1).total) * 100)}%`
            }}
          />
        </div>
      </div>

      {/* Phase 3 - VIA Character Strengths */}
      <div className={`rounded-xl p-4 mb-2 border cursor-pointer transition-all ${
        currentAssessmentIndex === 2
          ? 'bg-[#f5f7fb] border-[#e7eaff]'
          : 'bg-white border-[#eaecf0] hover:bg-[#f9fafb]'
      }`}>
        <div
          className="flex items-center justify-between mb-1"
          onClick={() => handlePhaseClick(2)}
        >
          <span className="font-semibold text-sm">Phase 3</span>
          <span className="text-xs text-[#64707d] font-medium">
            {getSectionProgress(2).answered}/{getSectionProgress(2).total}
          </span>
        </div>
        <div className="text-xs text-[#64707d] mb-2">VIA Character Strengths</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full">
          <div
            className="h-2 bg-[#6475e9] rounded-full transition-all"
            style={{
              width: `${Math.round((getSectionProgress(2).answered / getSectionProgress(2).total) * 100)}%`
            }}
          />
        </div>
      </div>

      {/* Total Progress */}
      <div className="mt-auto pt-4">
        <div className="text-center text-sm font-semibold mb-2">Total Progress</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full">
          <div
            className="h-2 bg-[#6475e9] rounded-full transition-all"
            style={{ width: `${progress.overallProgress}%` }}
          />
        </div>
        <div className="text-center text-xs text-[#64707d] mt-1">{progress.overallProgress}% Complete</div>
      </div>
    </aside>
  );
}
