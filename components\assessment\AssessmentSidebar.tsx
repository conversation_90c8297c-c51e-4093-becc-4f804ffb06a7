export default function AssessmentSidebar() {
  return (
    <aside className="w-[280px] bg-white border-r border-[#eaecf0] p-6 flex flex-col gap-6">
      <h2 className="font-bold text-lg mb-4">Assessment Progress</h2>
      {/* Phase 1 */}
      <div className="bg-[#f5f7fb] rounded-xl p-4 mb-2 border border-[#e7eaff]">
        <div className="flex items-center justify-between mb-1">
          <span className="font-semibold text-sm">Phase 1</span>
          <span className="text-xs text-[#64707d] font-medium">0/44</span>
        </div>
        <div className="text-xs text-[#64707d] mb-2">Big Five Personality</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full mb-2">
          <div className="h-2 bg-[#6475e9] rounded-full" style={{ width: '0%' }} />
        </div>
        {/* Sub-phases */}
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between bg-[#e7eaff] rounded-lg px-3 py-2">
            <span className="text-[#6475e9] text-sm font-semibold">Openness to Experience</span>
            <span className="bg-white rounded-full px-2 py-1 text-xs text-[#6475e9] font-bold border border-[#e7eaff]">0/10</span>
          </div>
          <div className="flex items-center justify-between px-3 py-2">
            <span className="text-[#64707d] text-sm">Conscientiousness</span>
            <span className="bg-[#f5f7fb] rounded-full px-2 py-1 text-xs text-[#64707d] font-bold border border-[#eaecf0]">0/9</span>
          </div>
          <div className="flex items-center justify-between px-3 py-2">
            <span className="text-[#64707d] text-sm">Extraversion</span>
            <span className="bg-[#f5f7fb] rounded-full px-2 py-1 text-xs text-[#64707d] font-bold border border-[#eaecf0]">0/8</span>
          </div>
          <div className="flex items-center justify-between px-3 py-2">
            <span className="text-[#64707d] text-sm">Agreeableness</span>
            <span className="bg-[#f5f7fb] rounded-full px-2 py-1 text-xs text-[#64707d] font-bold border border-[#eaecf0]">0/9</span>
          </div>
          <div className="flex items-center justify-between px-3 py-2">
            <span className="text-[#64707d] text-sm">Neuroticism</span>
            <span className="bg-[#f5f7fb] rounded-full px-2 py-1 text-xs text-[#64707d] font-bold border border-[#eaecf0]">0/8</span>
          </div>
        </div>
      </div>
      {/* Phase 2 */}
      <div className="bg-[#f5f7fb] rounded-xl p-4 mb-2 border border-[#e7eaff]">
        <div className="flex items-center justify-between mb-1">
          <span className="font-semibold text-sm">Phase 2</span>
          <span className="text-xs text-[#64707d] font-medium">0/60</span>
        </div>
        <div className="text-xs text-[#64707d] mb-2">RIASEC Holland Codes</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full">
          <div className="h-2 bg-[#6475e9] rounded-full" style={{ width: '0%' }} />
        </div>
      </div>
      {/* Phase 3 */}
      <div className="bg-[#f5f7fb] rounded-xl p-4 mb-2 border border-[#e7eaff]">
        <div className="flex items-center justify-between mb-1">
          <span className="font-semibold text-sm">Phase 3</span>
          <span className="text-xs text-[#64707d] font-medium">0/96</span>
        </div>
        <div className="text-xs text-[#64707d] mb-2">VIA Character Strengths</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full">
          <div className="h-2 bg-[#6475e9] rounded-full" style={{ width: '0%' }} />
        </div>
      </div>
      {/* Total Progress */}
      <div className="mt-auto pt-4">
        <div className="text-center text-sm font-semibold mb-2">Total Progress</div>
        <div className="w-full h-2 bg-[#eaecf0] rounded-full">
          <div className="h-2 bg-[#6475e9] rounded-full" style={{ width: '0%' }} />
        </div>
        <div className="text-center text-xs text-[#64707d] mt-1">0% Complete</div>
      </div>
    </aside>
  );
}
