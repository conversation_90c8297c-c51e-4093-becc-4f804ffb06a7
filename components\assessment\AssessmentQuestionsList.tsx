import { assessmentTypes, scaleConfigurations } from '../../data/assessmentQuestions';
import AssessmentQuestionCard from './AssessmentQuestionCard';

import { useState } from "react";

export default function AssessmentQuestionsList() {
  const currentAssessment = assessmentTypes[0]; // Big Five
  const scaleConfig = scaleConfigurations[currentAssessment.scaleType];
  // Group questions by category
  const grouped = currentAssessment.questions.reduce((acc, q) => {
    acc[q.category] = acc[q.category] || [];
    acc[q.category].push(q);
    return acc;
  }, {} as Record<string, any[]>);

  const categories = Object.keys(grouped);
  const [currentSectionIdx, setCurrentSectionIdx] = useState(0);
  const [answers, setAnswers] = useState<Record<number, number | null>>({});

  // Handler to update answer for a question
  const handleAnswer = (questionId: number, value: number) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }));
  };

  // Paging controls
  const handlePrevSection = () => {
    if (currentSectionIdx > 0) setCurrentSectionIdx(currentSectionIdx - 1);
  };
  const handleNextSection = () => {
    if (currentSectionIdx < categories.length - 1) setCurrentSectionIdx(currentSectionIdx + 1);
  };

  // Current section data
  const category = categories[currentSectionIdx];
  const questions = grouped[category];
  const answered = questions.filter(q => answers[q.id] != null).length;
  const total = questions.length;
  const percent = Math.round((answered / total) * 100);

  return (
    <div className="flex flex-col items-center w-full">
      <div className="w-full mb-12">
        <div className="mb-2">
          <span className="font-bold text-lg text-[#6475e9]">{category}</span>
          <span className="ml-2 text-sm text-[#64707d]">{answered}/{total}</span>
        </div>
        {/* Progress bar removed, use sidebar only */}
        <div className="flex flex-col ">
          {questions.map((question, idx) => {
            const isLast = idx === questions.length - 1;
            return (
              <div key={question.id}>
                <AssessmentQuestionCard
                  question={question}
                  scaleConfig={scaleConfig}
                  scaleLabels={currentAssessment.scaleLabels}
                  selectedAnswer={answers[question.id] ?? null}
                  onAnswer={value => handleAnswer(question.id, value)}
                />
                {isLast && (
                  <>
                    <div className="border-t border-[#eaecf0] my-4" />
                    <div className="flex items-center justify-between mt-4 px-2">
                      <button
                        onClick={handlePrevSection}
                        disabled={currentSectionIdx === 0}
                        className={`px-6 py-2 rounded-lg border font-medium flex items-center gap-2 ${
                          currentSectionIdx === 0
                            ? 'border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] cursor-not-allowed'
                            : 'border-[#6475e9] text-[#6475e9] bg-white hover:bg-[#f2f4ff]'
                        }`}
                      >
                        <span className="text-lg">&larr;</span> <span>Sebelumnya</span>
                      </button>
                      <div className="flex items-center gap-4">
                        <button className="px-0 py-2 text-[#64707d] bg-transparent font-medium border-none">Lewati</button>
                        <button
                          onClick={handleNextSection}
                          disabled={currentSectionIdx === categories.length - 1}
                          className={`px-6 py-2 rounded-lg border font-semibold flex items-center gap-2 ${
                            currentSectionIdx === categories.length - 1
                              ? 'border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] cursor-not-allowed'
                              : 'border-[#6475e9] text-white bg-[#6475e9] hover:bg-[#5a6fd8]'
                          }`}
                        >
                          <span>Selanjutnya</span>
                          <span className="ml-1 text-lg">&rarr;</span>
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
