'use client';

import { scaleConfigurations } from '../../data/assessmentQuestions';
import AssessmentQuestionCard from './AssessmentQuestionCard';
import { useAssessment } from '../../contexts/AssessmentContext';

export default function AssessmentQuestionsList() {
  const {
    getCurrentAssessment,
    getCurrentSection,
    currentSectionIndex,
    setCurrentSectionIndex,
    answers,
    setAnswer
  } = useAssessment();

  const currentAssessment = getCurrentAssessment();
  const scaleConfig = scaleConfigurations[currentAssessment.scaleType];

  // Group questions by category
  const grouped = currentAssessment.questions.reduce((acc: any, q: any) => {
    acc[q.category] = acc[q.category] || [];
    acc[q.category].push(q);
    return acc;
  }, {});

  const categories = Object.keys(grouped);

  // Handler to update answer for a question
  const handleAnswer = (questionId: number, value: number) => {
    setAnswer(questionId, value);
  };

  // Paging controls
  const handlePrevSection = () => {
    if (currentSectionIndex > 0) setCurrentSectionIndex(currentSectionIndex - 1);
  };
  const handleNextSection = () => {
    if (currentSectionIndex < categories.length - 1) setCurrentSectionIndex(currentSectionIndex + 1);
  };

  // Current section data
  const category = categories[currentSectionIndex];
  const questions = grouped[category];
  const answered = questions.filter((q: any) => answers[q.id] != null).length;
  const total = questions.length;
  const percent = Math.round((answered / total) * 100);

  return (
    <div className="flex flex-col items-center w-full">
      <div className="w-full mb-12">
        <div className="mb-2">
          <span className="font-bold text-lg text-[#6475e9]">{category}</span>
          <span className="ml-2 text-sm text-[#64707d]">{answered}/{total}</span>
        </div>
        {/* Progress bar removed, use sidebar only */}
        <div className="flex flex-col ">
          {questions.map((question, idx) => {
            const isLast = idx === questions.length - 1;
            return (
              <div key={question.id}>
                <AssessmentQuestionCard
                  question={question}
                  scaleConfig={scaleConfig}
                  scaleLabels={currentAssessment.scaleLabels}
                  selectedAnswer={answers[question.id] ?? null}
                  onAnswer={value => handleAnswer(question.id, value)}
                  isLastQuestion={isLast}
                  navigationButtons={isLast ? (
                    <div className="flex items-center justify-between mt-2 px-2">
                      <button
                        onClick={handlePrevSection}
                        disabled={currentSectionIndex === 0}
                        className={`px-6 py-2 rounded-lg border font-medium flex items-center gap-2 ${
                          currentSectionIndex === 0
                            ? 'border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] cursor-not-allowed'
                            : 'border-[#6475e9] text-[#6475e9] bg-white hover:bg-[#f2f4ff]'
                        }`}
                      >
                        <span className="text-lg">&larr;</span> <span>Sebelumnya</span>
                      </button>
                      <div className="flex items-center gap-4">
                        <button className="px-0 py-2 text-[#64707d] bg-transparent font-medium border-none">Lewati</button>
                        <button
                          onClick={handleNextSection}
                          disabled={currentSectionIndex === categories.length - 1}
                          className={`px-6 py-2 rounded-lg border font-semibold flex items-center gap-2 ${
                            currentSectionIndex === categories.length - 1
                              ? 'border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] cursor-not-allowed'
                              : 'border-[#6475e9] text-white bg-[#6475e9] hover:bg-[#5a6fd8]'
                          }`}
                        >
                          <span>Selanjutnya</span>
                          <span className="ml-1 text-lg">&rarr;</span>
                        </button>
                      </div>
                    </div>
                  ) : undefined}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
