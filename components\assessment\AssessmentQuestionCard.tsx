'use client';

import { useState } from 'react';
import { bigFiveQuestions, assessmentTypes, scaleConfigurations } from '../../data/assessmentQuestions';

export default function AssessmentQuestionCard() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);

  // For demo purposes, using Big Five assessment
  const currentAssessment = assessmentTypes[0]; // Big Five
  const currentQuestion = currentAssessment.questions[currentQuestionIndex];
  const scaleConfig = scaleConfigurations[currentAssessment.scaleType];

  const handleAnswerSelect = (value: number) => {
    setSelectedAnswer(value);
  };

  const handleNext = () => {
    if (currentQuestionIndex < currentAssessment.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setSelectedAnswer(null);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-8 w-[1200px] h-[430px] mx-auto flex flex-col gap-4">
      {/* Top badge and question */}
      <div className="mb-2">
        <span className="inline-block px-4 py-1 rounded-full bg-[#e7eaff] text-[#6475e9] text-s font-semibold mb-2">
          {currentQuestion.subcategory}
        </span>
        <h3 className="font-bold text-2xl mb-2 mt-2">{currentQuestion.text}</h3>
        <p className="text-[#64707d] text-base mb-4">
          Pilih seberapa setuju Anda dengan pernyataan di atas menggunakan skala 1-{scaleConfig.values.length}
        </p>
      </div>
      {/* Radio scale */}
      <div className="flex flex-row items-end justify-between gap-2 mb-2 px-2">
        {scaleConfig.values.map((value, idx) => (
          <div key={value} className="flex flex-col items-center w-24">
            <span className={`text-xs font-medium mb-1 text-[${scaleConfig.colors[idx]}]`}>
              {currentAssessment.scaleLabels[idx]}
            </span>
            <span className={`text-xl font-bold text-[${scaleConfig.colors[idx]}]`}>{value}</span>
            <input
              type="radio"
              name="answer"
              value={value}
              checked={selectedAnswer === value}
              onChange={() => handleAnswerSelect(value)}
              className="mt-2 w-6 h-6 accent-[#6475e9]"
            />
          </div>
        ))}
      </div>
      {/* Divider */}
      <div className="border-t border-[#eaecf0] my-4" />
      {/* Navigation buttons */}
      <div className="flex items-center justify-between mt-2 px-2">
        <button
          onClick={handlePrevious}
          disabled={currentQuestionIndex === 0}
          className={`px-6 py-2 rounded-lg border font-medium flex items-center gap-2 ${
            currentQuestionIndex === 0
              ? 'border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] cursor-not-allowed'
              : 'border-[#6475e9] text-[#6475e9] bg-white hover:bg-[#f2f4ff]'
          }`}
        >
          <span className="text-lg">&larr;</span> <span>Sebelumnya</span>
        </button>
        <div className="flex items-center gap-4">
          <span className="text-sm text-[#64707d]">
            Pertanyaan {currentQuestionIndex + 1} dari {currentAssessment.totalQuestions}
          </span>
          <button className="px-0 py-2 text-[#64707d] bg-transparent font-medium border-none">Lewati</button>
          <button
            onClick={handleNext}
            disabled={currentQuestionIndex === currentAssessment.questions.length - 1}
            className={`px-6 py-2 rounded-lg border font-semibold flex items-center gap-2 ${
              currentQuestionIndex === currentAssessment.questions.length - 1
                ? 'border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] cursor-not-allowed'
                : 'border-[#6475e9] text-white bg-[#6475e9] hover:bg-[#5a6fd8]'
            }`}
          >
            <span>Selanjutnya</span>
            <span className="ml-1 text-lg">&#8594;</span>
          </button>
        </div>
      </div>
    </div>
  );
}
