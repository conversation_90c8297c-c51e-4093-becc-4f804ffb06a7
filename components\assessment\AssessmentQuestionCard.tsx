export default function AssessmentQuestionCard() {
  return (
    <div className="bg-white rounded-xl shadow p-8 w-[1200px] h-[430px] mx-auto flex flex-col gap-4">
      {/* Top badge and question */}
      <div className="mb-2">
        <span className="inline-block px-4 py-1 rounded-full bg-[#e7eaff] text-[#6475e9] text-xs font-semibold mb-2">Openness to Experience</span>
        <h3 className="font-bold text-2xl mb-2 mt-2"><PERSON><PERSON> adalah seseorang yang is original, comes up with new ideas</h3>
        <p className="text-[#64707d] text-base mb-4"><PERSON><PERSON>h seberapa setuju Anda dengan pernyataan di atas menggunakan skala 1-7</p>
      </div>
      {/* Radio scale */}
      <div className="flex flex-row items-end justify-between gap-2 mb-2 px-2">
        {[
          {num: '1', label: 'Sangat Tidak Setuju', color: 'text-[#e53935]'},
          {num: '2', label: 'Tidak Setuju', color: 'text-[#e53935]'},
          {num: '3', label: 'Agak Tidak Setuju', color: 'text-[#e53935]'},
          {num: '4', label: 'Netral', color: 'text-[#64707d]'},
          {num: '5', label: 'Agak Setuju', color: 'text-[#43a047]'},
          {num: '6', label: 'Setuju', color: 'text-[#43a047]'},
          {num: '7', label: 'Sangat Setuju', color: 'text-[#43a047]'},
        ].map((item, idx) => (
          <div key={item.num} className="flex flex-col items-center w-24">
            <span className={`text-xs font-medium mb-1 ${item.color}`}>{item.label}</span>
            <span className={`text-xl font-bold ${item.color}`}>{item.num}</span>
            <input type="radio" name="answer" className="mt-2 w-6 h-6 accent-[#6475e9]" />
          </div>
        ))}
      </div>
      {/* Divider */}
      <div className="border-t border-[#eaecf0] my-4" />
      {/* Navigation buttons */}
      <div className="flex items-center justify-between mt-2 px-2">
        <button className="px-6 py-2 rounded-lg border border-[#eaecf0] text-[#64707d] bg-[#f5f7fb] font-medium flex items-center gap-2" disabled>
          <span className="text-lg">&larr;</span> <span>Sebelumnya</span>
        </button>
        <div className="flex items-center gap-4">
          <button className="px-0 py-2 text-[#64707d] bg-transparent font-medium border-none">Lewati</button>
          <button className="px-6 py-2 rounded-lg border border-[#6475e9] text-white bg-[#6475e9] font-semibold flex items-center gap-2">
            <span>Selanjutnya</span>
            <span className="ml-1 text-lg">&#8594;</span>
          </button>
        </div>
      </div>
    </div>
  );
}
