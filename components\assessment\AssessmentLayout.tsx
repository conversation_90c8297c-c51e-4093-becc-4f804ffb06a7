'use client';

import AssessmentSidebar from "./AssessmentSidebar";
import AssessmentHeader from "./AssessmentHeader";
import AssessmentProgressBar from "./AssessmentProgressBar";
import AssessmentQuestionsList from "./AssessmentQuestionsList";
import { AssessmentProvider, useAssessment } from '../../contexts/AssessmentContext';

function AssessmentContent() {
  const { getCurrentAssessment, currentSectionIndex } = useAssessment();
  const currentAssessment = getCurrentAssessment();

  return (
    <div className="flex flex-row-reverse min-h-screen bg-[#f5f7fb]">
      <AssessmentSidebar />
      <div className="flex-1 flex flex-col">
        <AssessmentHeader
          currentQuestion={currentSectionIndex + 1}
          totalQuestions={currentAssessment.totalQuestions}
          assessmentName={currentAssessment.name}
          phase={`Phase ${currentAssessment.id === 'big-five' ? '1' : currentAssessment.id === 'riasec' ? '2' : '3'}`}
        />
        <AssessmentProgressBar />
        <div className="flex-1 flex flex-col items-center justify-start overflow-y-auto py-8">
          {/* Render all questions vertically */}
          <AssessmentQuestionsList />
        </div>
      </div>
    </div>
  );
}

export default function AssessmentLayout() {
  return (
    <AssessmentProvider>
      <AssessmentContent />
    </AssessmentProvider>
  );
}
