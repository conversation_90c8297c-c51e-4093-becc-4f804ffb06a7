'use client';

import { useState } from 'react';
import AssessmentSidebar from "./AssessmentSidebar";
import AssessmentHeader from "./AssessmentHeader";
import AssessmentProgressBar from "./AssessmentProgressBar";
import AssessmentQuestionCard from "./AssessmentQuestionCard";
import { assessmentTypes } from '../../data/assessmentQuestions';

export default function AssessmentLayout() {
  const [currentAssessmentIndex] = useState(0); // Start with Big Five
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  const currentAssessment = assessmentTypes[currentAssessmentIndex];

  return (
    <div className="flex flex-row-reverse min-h-screen bg-[#f5f7fb]">
      <AssessmentSidebar />
      <div className="flex-1 flex flex-col">
        <AssessmentHeader
          currentQuestion={currentQuestionIndex + 1}
          totalQuestions={currentAssessment.totalQuestions}
          assessmentName={currentAssessment.name}
          phase="Phase 1"
        />
        <AssessmentProgressBar />
        <div className="flex-1 flex items-center justify-center">
          <AssessmentQuestionCard />
        </div>
      </div>
    </div>
  );
}
