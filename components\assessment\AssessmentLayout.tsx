import AssessmentSidebar from "./AssessmentSidebar";
import AssessmentHeader from "./AssessmentHeader";
import AssessmentProgressBar from "./AssessmentProgressBar";
import AssessmentQuestionCard from "./AssessmentQuestionCard";

export default function AssessmentLayout() {
  return (
    <div className="flex flex-row-reverse min-h-screen bg-[#f5f7fb]">
      <AssessmentSidebar />
      <div className="flex-1 flex flex-col">
        <AssessmentHeader />
        <AssessmentProgressBar />
        <div className="flex-1 flex items-center justify-center">
          <AssessmentQuestionCard />
        </div>
      </div>
    </div>
  );
}
