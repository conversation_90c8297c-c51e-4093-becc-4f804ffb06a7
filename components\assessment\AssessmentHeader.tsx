export default function AssessmentHeader() {
  return (
    <div className="flex items-center justify-between px-8 py-6 bg-transparent">
      <div className="flex items-center gap-2">
        <button className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E5E7EB] bg-white text-[#64707D] text-[16px] font-medium shadow-sm hover:bg-[#f5f7fb] transition" type="button">
          <img src="/icons/CaretLeft.svg" alt="Back" className="w-4 h-4" />
          Kembali ke Dashboard
        </button>
        <span className="font-semibold text-lg ml-4">Phase 1: Big Five Personality</span>
      </div>
      <div className="flex items-center gap-6">
        <span className="text-sm text-[#64707d]">Pertanyaan 1 dari 44</span>
        <button className="text-[#6475e9] text-sm font-semibold">Simpan & Keluar</button>
      </div>
    </div>
  );
}
