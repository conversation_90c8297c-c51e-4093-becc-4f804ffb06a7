// Assessment Questions Data
export interface Question {
  id: number;
  text: string;
  category: string;
  subcategory?: string;
  isReversed?: boolean;
}

export interface AssessmentType {
  id: string;
  name: string;
  description: string;
  totalQuestions: number;
  scaleType: '5-point' | '7-point';
  scaleLabels: string[];
  questions: Question[];
}

// Big Five Personality Assessment (BFI-44)
export const bigFiveQuestions: Question[] = [
  // Keterbukaan terhadap Pengalaman (Openness to Experience)
  { id: 1, text: "Saya melihat diri saya sebagai seseorang yang orisinal dan suka menciptakan ide-ide baru.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 2, text: "Saya melihat diri saya sebagai seseorang yang penasaran dengan banyak hal yang berbeda.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 3, text: "Saya melihat diri saya sebagai seseorang yang cerdik dan suka berpikir mendalam.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 4, text: "Saya melihat diri saya sebagai seseorang yang memiliki imajinasi yang aktif.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 5, text: "Saya melihat diri saya sebagai seseorang yang inventif (suka menciptakan sesuatu).", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 6, text: "Saya melihat diri saya sebagai seseorang yang menghargai pengalaman artistik dan estetika.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 7, text: "Saya melihat diri saya sebagai seseorang yang lebih suka pekerjaan yang rutin.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman", isReversed: true },
  { id: 8, text: "Saya melihat diri saya sebagai seseorang yang suka merenungkan dan bermain dengan ide-ide.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },
  { id: 9, text: "Saya melihat diri saya sebagai seseorang yang memiliki sedikit minat artistik.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman", isReversed: true },
  { id: 10, text: "Saya melihat diri saya sebagai seseorang yang memiliki pemahaman mendalam tentang seni, musik, atau sastra.", category: "Openness to Experience", subcategory: "Keterbukaan terhadap Pengalaman" },

  // Kehati-hatian (Conscientiousness)
  { id: 11, text: "Saya melihat diri saya sebagai seseorang yang mengerjakan sesuatu dengan teliti.", category: "Conscientiousness", subcategory: "Kehati-hatian" },
  { id: 12, text: "Saya melihat diri saya sebagai seseorang yang terkadang agak ceroboh.", category: "Conscientiousness", subcategory: "Kehati-hatian", isReversed: true },
  { id: 13, text: "Saya melihat diri saya sebagai seseorang yang dapat diandalkan dalam bekerja.", category: "Conscientiousness", subcategory: "Kehati-hatian" },
  { id: 14, text: "Saya melihat diri saya sebagai seseorang yang cenderung tidak teratur.", category: "Conscientiousness", subcategory: "Kehati-hatian", isReversed: true },
  { id: 15, text: "Saya melihat diri saya sebagai seseorang yang cenderung malas.", category: "Conscientiousness", subcategory: "Kehati-hatian", isReversed: true },
  { id: 16, text: "Saya melihat diri saya sebagai seseorang yang bertahan sampai tugas selesai.", category: "Conscientiousness", subcategory: "Kehati-hatian" },
  { id: 17, text: "Saya melihat diri saya sebagai seseorang yang mengerjakan sesuatu dengan efisien.", category: "Conscientiousness", subcategory: "Kehati-hatian" },
  { id: 18, text: "Saya melihat diri saya sebagai seseorang yang membuat rencana dan menjalankannya.", category: "Conscientiousness", subcategory: "Kehati-hatian" },
  { id: 19, text: "Saya melihat diri saya sebagai seseorang yang mudah terganggu konsentrasinya.", category: "Conscientiousness", subcategory: "Kehati-hatian", isReversed: true },

  // Ekstraversi (Extraversion)
  { id: 20, text: "Saya melihat diri saya sebagai seseorang yang suka berbicara.", category: "Extraversion", subcategory: "Ekstraversi" },
  { id: 21, text: "Saya melihat diri saya sebagai seseorang yang pendiam/tertutup.", category: "Extraversion", subcategory: "Ekstraversi", isReversed: true },
  { id: 22, text: "Saya melihat diri saya sebagai seseorang yang penuh energi.", category: "Extraversion", subcategory: "Ekstraversi" },
  { id: 23, text: "Saya melihat diri saya sebagai seseorang yang dapat membangkitkan antusiasme.", category: "Extraversion", subcategory: "Ekstraversi" },
  { id: 24, text: "Saya melihat diri saya sebagai seseorang yang cenderung pendiam.", category: "Extraversion", subcategory: "Ekstraversi", isReversed: true },
  { id: 25, text: "Saya melihat diri saya sebagai seseorang yang memiliki kepribadian tegas.", category: "Extraversion", subcategory: "Ekstraversi" },
  { id: 26, text: "Saya melihat diri saya sebagai seseorang yang terkadang pemalu dan terhambat.", category: "Extraversion", subcategory: "Ekstraversi", isReversed: true },
  { id: 27, text: "Saya melihat diri saya sebagai seseorang yang ramah dan mudah bergaul.", category: "Extraversion", subcategory: "Ekstraversi" },

  // Keramahan (Agreeableness)
  { id: 28, text: "Saya melihat diri saya sebagai seseorang yang cenderung mencari kesalahan orang lain.", category: "Agreeableness", subcategory: "Keramahan", isReversed: true },
  { id: 29, text: "Saya melihat diri saya sebagai seseorang yang suka menolong dan tidak egois terhadap orang lain.", category: "Agreeableness", subcategory: "Keramahan" },
  { id: 30, text: "Saya melihat diri saya sebagai seseorang yang suka memulai pertengkaran dengan orang lain.", category: "Agreeableness", subcategory: "Keramahan", isReversed: true },
  { id: 31, text: "Saya melihat diri saya sebagai seseorang yang memiliki sifat pemaaf.", category: "Agreeableness", subcategory: "Keramahan" },
  { id: 32, text: "Saya melihat diri saya sebagai seseorang yang umumnya mudah percaya.", category: "Agreeableness", subcategory: "Keramahan" },
  { id: 33, text: "Saya melihat diri saya sebagai seseorang yang bisa bersikap dingin dan menjaga jarak.", category: "Agreeableness", subcategory: "Keramahan", isReversed: true },
  { id: 34, text: "Saya melihat diri saya sebagai seseorang yang perhatian dan baik kepada hampir semua orang.", category: "Agreeableness", subcategory: "Keramahan" },
  { id: 35, text: "Saya melihat diri saya sebagai seseorang yang terkadang kasar kepada orang lain.", category: "Agreeableness", subcategory: "Keramahan", isReversed: true },
  { id: 36, text: "Saya melihat diri saya sebagai seseorang yang suka bekerja sama dengan orang lain.", category: "Agreeableness", subcategory: "Keramahan" },

  // Neurotisisme (Neuroticism)
  { id: 37, text: "Saya melihat diri saya sebagai seseorang yang mudah depresi atau sedih.", category: "Neuroticism", subcategory: "Neurotisisme" },
  { id: 38, text: "Saya melihat diri saya sebagai seseorang yang santai dan dapat mengatasi stres dengan baik.", category: "Neuroticism", subcategory: "Neurotisisme", isReversed: true },
  { id: 39, text: "Saya melihat diri saya sebagai seseorang yang bisa tegang.", category: "Neuroticism", subcategory: "Neurotisisme" },
  { id: 40, text: "Saya melihat diri saya sebagai seseorang yang sering khawatir.", category: "Neuroticism", subcategory: "Neurotisisme" },
  { id: 41, text: "Saya melihat diri saya sebagai seseorang yang stabil secara emosional dan tidak mudah marah.", category: "Neuroticism", subcategory: "Neurotisisme", isReversed: true },
  { id: 42, text: "Saya melihat diri saya sebagai seseorang yang bisa moody (mudah berubah suasana hati).", category: "Neuroticism", subcategory: "Neurotisisme" },
  { id: 43, text: "Saya melihat diri saya sebagai seseorang yang tetap tenang dalam situasi tegang.", category: "Neuroticism", subcategory: "Neurotisisme", isReversed: true },
  { id: 44, text: "Saya melihat diri saya sebagai seseorang yang mudah gugup.", category: "Neuroticism", subcategory: "Neurotisisme" },
];

// RIASEC Holland Codes Questions
export const riasecQuestions: Question[] = [
  // Realistis (R) - "Para Pelaksana"
  { id: 1, text: "Saya senang bekerja dengan tangan, menggunakan alat, atau mengoperasikan mesin.", category: "Realistic", subcategory: "Realistis" },
  { id: 2, text: "Membangun, memperbaiki, atau memperbaiki sesuatu memberikan kepuasan bagi saya.", category: "Realistic", subcategory: "Realistis" },
  { id: 3, text: "Saya lebih suka bekerja di luar ruangan daripada di kantor.", category: "Realistic", subcategory: "Realistis" },
  { id: 4, text: "Saya adalah orang yang praktis dan suka melihat hasil nyata dari pekerjaan saya.", category: "Realistic", subcategory: "Realistis" },
  { id: 5, text: "Saya pandai dalam tugas-tugas mekanis dan memahami cara kerja sesuatu.", category: "Realistic", subcategory: "Realistis" },
  { id: 6, text: "Saya suka aktivitas seperti berkebun, konstruksi, atau mengerjakan mobil.", category: "Realistic", subcategory: "Realistis" },
  { id: 7, text: "Saya lebih suka masalah yang konkret daripada yang abstrak.", category: "Realistic", subcategory: "Realistis" },
  { id: 8, text: "Saya lebih suka bekerja dengan benda daripada dengan orang atau ide.", category: "Realistic", subcategory: "Realistis" },
  { id: 9, text: "Saya adalah orang yang suka petualangan dan menikmati tantangan fisik.", category: "Realistic", subcategory: "Realistis" },
  { id: 10, text: "Saya belajar paling baik dengan melakukan langsung.", category: "Realistic", subcategory: "Realistis" },

  // Investigatif (I) - "Para Pemikir"
  { id: 11, text: "Saya penasaran tentang dunia fisik dan alam.", category: "Investigative", subcategory: "Investigatif" },
  { id: 12, text: "Saya senang memecahkan masalah kompleks yang membutuhkan banyak pemikiran.", category: "Investigative", subcategory: "Investigatif" },
  { id: 13, text: "Saya suka melakukan penelitian, menganalisis data, dan memahami teori.", category: "Investigative", subcategory: "Investigatif" },
  { id: 14, text: "Saya pandai dalam matematika dan sains.", category: "Investigative", subcategory: "Investigatif" },
  { id: 15, text: "Saya senang melakukan aktivitas seperti membaca jurnal ilmiah, mengerjakan teka-teki, atau melakukan eksperimen.", category: "Investigative", subcategory: "Investigatif" },
  { id: 16, text: "Saya adalah orang yang analitis dan logis.", category: "Investigative", subcategory: "Investigatif" },
  { id: 17, text: "Saya lebih suka bekerja secara mandiri dan mengejar ide-ide saya sendiri.", category: "Investigative", subcategory: "Investigatif" },
  { id: 18, text: "Saya menghargai ketepatan dan akurasi dalam pekerjaan saya.", category: "Investigative", subcategory: "Investigatif" },
  { id: 19, text: "Saya lebih suka bekerja dengan ide daripada dengan orang atau benda.", category: "Investigative", subcategory: "Investigatif" },
  { id: 20, text: "Saya terdorong untuk memahami mengapa sesuatu terjadi.", category: "Investigative", subcategory: "Investigatif" },

  // Artistik (A) - "Para Pencipta"
  { id: 21, text: "Saya memiliki imajinasi yang baik dan senang mengekspresikan diri secara kreatif.", category: "Artistic", subcategory: "Artistik" },
  { id: 22, text: "Saya suka bekerja di lingkungan yang tidak terstruktur di mana saya bisa menjadi orisinal.", category: "Artistic", subcategory: "Artistik" },
  { id: 23, text: "Saya senang melakukan aktivitas seperti melukis, menulis, bermain musik, atau menari.", category: "Artistic", subcategory: "Artistik" },
  { id: 24, text: "Saya adalah orang yang ekspresif, orisinal, dan mandiri.", category: "Artistic", subcategory: "Artistik" },
  { id: 25, text: "Saya menghargai seni, musik, dan sastra.", category: "Artistic", subcategory: "Artistik" },
  { id: 26, text: "Saya lebih suka bekerja dengan ide dan benda daripada dengan orang.", category: "Artistic", subcategory: "Artistik" },
  { id: 27, text: "Saya tidak suka aturan atau rutinitas yang ketat.", category: "Artistic", subcategory: "Artistik" },
  { id: 28, text: "Saya melihat dunia melalui sudut pandang yang kreatif dan tidak konvensional.", category: "Artistic", subcategory: "Artistik" },
  { id: 29, text: "Saya pandai dalam menciptakan ide-ide baru.", category: "Artistic", subcategory: "Artistik" },
  { id: 30, text: "Ekspresi diri sangat penting bagi saya.", category: "Artistic", subcategory: "Artistik" },

  // Sosial (S) - "Para Penolong"
  { id: 31, text: "Saya senang membantu, mengajar, atau memberikan konseling kepada orang lain.", category: "Social", subcategory: "Sosial" },
  { id: 32, text: "Saya pandai memahami dan berempati dengan perasaan orang lain.", category: "Social", subcategory: "Sosial" },
  { id: 33, text: "Saya suka bekerja dalam kelompok dan berkolaborasi dengan orang lain.", category: "Social", subcategory: "Sosial" },
  { id: 34, text: "Saya adalah orang yang ramah, suka menolong, dan kooperatif.", category: "Social", subcategory: "Sosial" },
  { id: 35, text: "Saya menghargai hubungan dan membuat perbedaan di komunitas saya.", category: "Social", subcategory: "Sosial" },
  { id: 36, text: "Saya lebih suka bekerja dengan orang daripada dengan benda atau ide.", category: "Social", subcategory: "Sosial" },
  { id: 37, text: "Saya adalah pendengar yang baik dan orang sering datang kepada saya dengan masalah mereka.", category: "Social", subcategory: "Sosial" },
  { id: 38, text: "Saya senang melakukan aktivitas seperti volunteering, mentoring, atau layanan pelanggan.", category: "Social", subcategory: "Sosial" },
  { id: 39, text: "Saya terampil dalam berkomunikasi dan menyelesaikan konflik.", category: "Social", subcategory: "Sosial" },
  { id: 40, text: "Membuat orang merasa nyaman dan didukung adalah hal yang penting bagi saya.", category: "Social", subcategory: "Sosial" },

  // Enterprising/Kewirausahaan (E) - "Para Persuader"
  { id: 41, text: "Saya senang memimpin, membujuk, dan memotivasi orang lain.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 42, text: "Saya ambisius dan senang mengambil tantangan untuk mendapatkan imbalan yang tinggi.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 43, text: "Saya pandai berbicara di depan umum dan menjual ide atau produk.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 44, text: "Saya adalah orang yang energik, percaya diri, dan tegas.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 45, text: "Saya suka bekerja di posisi yang berpengaruh dan memiliki otoritas.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 46, text: "Saya lebih suka bekerja dengan orang dan data daripada dengan benda.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 47, text: "Saya tertarik pada bisnis, politik, dan keuangan.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 48, text: "Saya senang mengambil risiko dan membuat keputusan.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 49, text: "Saya pandai mengorganisir dan mengelola proyek atau orang.", category: "Enterprising", subcategory: "Kewirausahaan" },
  { id: 50, text: "Saya kompetitif dan suka menang.", category: "Enterprising", subcategory: "Kewirausahaan" },

  // Konvensional (C) - "Para Pengorganisir"
  { id: 51, text: "Saya senang bekerja dengan data, angka, dan detail.", category: "Conventional", subcategory: "Konvensional" },
  { id: 52, text: "Saya suka bekerja di lingkungan yang terstruktur dengan aturan dan prosedur yang jelas.", category: "Conventional", subcategory: "Konvensional" },
  { id: 53, text: "Saya pandai mengorganisir informasi, menyimpan catatan, dan mengikuti rencana.", category: "Conventional", subcategory: "Konvensional" },
  { id: 54, text: "Saya adalah orang yang hati-hati, teratur, dan bertanggung jawab.", category: "Conventional", subcategory: "Konvensional" },
  { id: 55, text: "Saya menghargai akurasi dan efisiensi.", category: "Conventional", subcategory: "Konvensional" },
  { id: 56, text: "Saya lebih suka bekerja dengan data dan benda daripada dengan ide.", category: "Conventional", subcategory: "Konvensional" },
  { id: 57, text: "Saya pandai dalam tugas-tugas yang membutuhkan ketepatan dan perhatian terhadap detail.", category: "Conventional", subcategory: "Konvensional" },
  { id: 58, text: "Saya lebih suka memiliki instruksi yang jelas dan tahu persis apa yang diharapkan dari saya.", category: "Conventional", subcategory: "Konvensional" },
  { id: 59, text: "Saya senang melakukan aktivitas seperti akuntansi, pemrograman, atau mengelola database.", category: "Conventional", subcategory: "Konvensional" },
  { id: 60, text: "Saya dapat diandalkan dan menganggap serius tanggung jawab saya.", category: "Conventional", subcategory: "Konvensional" },
];

// VIA Character Strengths Questions (96 questions - showing first 48 for brevity)
export const viaQuestions: Question[] = [
  // Kebijaksanaan (Wisdom) - Kreativitas
  { id: 1, text: "Saya selalu menemukan cara-cara baru untuk melakukan sesuatu.", category: "Wisdom", subcategory: "Kreativitas" },
  { id: 2, text: "Memikirkan ide-ide baru adalah salah satu kekuatan terbesar saya.", category: "Wisdom", subcategory: "Kreativitas" },
  { id: 3, text: "Saya adalah pemikir yang orisinal.", category: "Wisdom", subcategory: "Kreativitas" },
  { id: 4, text: "Ketika saya memiliki masalah, saya senang menemukan solusi kreatif.", category: "Wisdom", subcategory: "Kreativitas" },

  // Kebijaksanaan (Wisdom) - Rasa Ingin Tahu
  { id: 5, text: "Saya selalu mengeksplorasi dunia dan bertanya 'mengapa?'", category: "Wisdom", subcategory: "Rasa Ingin Tahu" },
  { id: 6, text: "Saya menemukan banyak hal yang menarik.", category: "Wisdom", subcategory: "Rasa Ingin Tahu" },
  { id: 7, text: "Saya mudah bosan dengan hal-hal yang sudah pernah saya alami.", category: "Wisdom", subcategory: "Rasa Ingin Tahu" },
  { id: 8, text: "Saya suka mengunjungi tempat-tempat baru dan belajar tentang tempat tersebut.", category: "Wisdom", subcategory: "Rasa Ingin Tahu" },

  // Kebijaksanaan (Wisdom) - Penilaian/Berpikir Kritis
  { id: 9, text: "Saya selalu mempertimbangkan untung rugi sebelum membuat keputusan.", category: "Wisdom", subcategory: "Penilaian/Berpikir Kritis" },
  { id: 10, text: "Teman-teman saya datang kepada saya untuk meminta nasihat karena saya melihat hal-hal dengan jelas dan tanpa bias.", category: "Wisdom", subcategory: "Penilaian/Berpikir Kritis" },
  { id: 11, text: "Saya memastikan memiliki semua fakta sebelum membentuk opini.", category: "Wisdom", subcategory: "Penilaian/Berpikir Kritis" },
  { id: 12, text: "Memikirkan segala sesuatu dengan matang adalah bagian penting dari diri saya.", category: "Wisdom", subcategory: "Penilaian/Berpikir Kritis" },

  // Kebijaksanaan (Wisdom) - Cinta Belajar
  { id: 13, text: "Saya sangat senang ketika bisa mempelajari sesuatu yang baru.", category: "Wisdom", subcategory: "Cinta Belajar" },
  { id: 14, text: "Saya menikmati tantangan menguasai subjek yang sulit.", category: "Wisdom", subcategory: "Cinta Belajar" },
  { id: 15, text: "Saya adalah pembelajar seumur hidup.", category: "Wisdom", subcategory: "Cinta Belajar" },
  { id: 16, text: "Saya sering membaca buku atau artikel untuk meningkatkan pengetahuan saya.", category: "Wisdom", subcategory: "Cinta Belajar" },

  // Kebijaksanaan (Wisdom) - Perspektif
  { id: 17, text: "Orang-orang menggambarkan saya sebagai orang yang bijaksana.", category: "Wisdom", subcategory: "Perspektif" },
  { id: 18, text: "Saya memiliki kemampuan untuk melihat gambaran besar dalam situasi yang kompleks.", category: "Wisdom", subcategory: "Perspektif" },
  { id: 19, text: "Saya mampu melihat hal-hal dari berbagai sudut pandang yang berbeda, yang membantu saya memberikan nasihat yang baik.", category: "Wisdom", subcategory: "Perspektif" },
  { id: 20, text: "Saya sering dapat menemukan cara untuk memahami dunia ketika orang lain bingung.", category: "Wisdom", subcategory: "Perspektif" },

  // Keberanian (Courage) - Keberanian
  { id: 21, text: "Saya adalah orang yang berani, bahkan ketika saya merasa takut.", category: "Courage", subcategory: "Keberanian" },
  { id: 22, text: "Saya selalu membela apa yang benar, meskipun itu berarti menghadapi perlawanan.", category: "Courage", subcategory: "Keberanian" },
  { id: 23, text: "Saya tidak mundur dari ancaman atau tantangan.", category: "Courage", subcategory: "Keberanian" },
  { id: 24, text: "Menghadapi ketakutan saya membuat saya merasa lebih kuat.", category: "Courage", subcategory: "Keberanian" },

  // Keberanian (Courage) - Ketekunan
  { id: 25, text: "Saya tidak pernah menyerah pada tujuan setelah saya memulainya.", category: "Courage", subcategory: "Ketekunan" },
  { id: 26, text: "Saya adalah pekerja keras dan menyelesaikan apa yang saya mulai.", category: "Courage", subcategory: "Ketekunan" },
  { id: 27, text: "Kemunduran tidak membuat saya putus asa; justru membuat saya bekerja lebih keras.", category: "Courage", subcategory: "Ketekunan" },
  { id: 28, text: "Saya rajin dan disiplin dalam pekerjaan saya.", category: "Courage", subcategory: "Ketekunan" },

  // Keberanian (Courage) - Kejujuran
  { id: 29, text: "Saya menjalani hidup saya dengan cara yang tulus dan autentik.", category: "Courage", subcategory: "Kejujuran" },
  { id: 30, text: "Berkata jujur lebih penting bagi saya daripada menjadi populer.", category: "Courage", subcategory: "Kejujuran" },
  { id: 31, text: "Saya adalah orang yang apa adanya; saya tidak berpura-pura menjadi sesuatu yang bukan diri saya.", category: "Courage", subcategory: "Kejujuran" },
  { id: 32, text: "Teman dan keluarga saya tahu bahwa mereka dapat mengandalkan saya untuk bersikap terus terang.", category: "Courage", subcategory: "Kejujuran" },

  // Keberanian (Courage) - Semangat Hidup
  { id: 33, text: "Saya bangun dengan perasaan bersemangat untuk memulai hari.", category: "Courage", subcategory: "Semangat Hidup" },
  { id: 34, text: "Saya mendekati segala sesuatu yang saya lakukan dengan energi dan antusiasme.", category: "Courage", subcategory: "Semangat Hidup" },
  { id: 35, text: "Saya merasa hidup dan penuh vitalitas.", category: "Courage", subcategory: "Semangat Hidup" },
  { id: 36, text: "Saya ingin menjalani hidup sepenuhnya.", category: "Courage", subcategory: "Semangat Hidup" },

  // Kemanusiaan (Humanity) - Cinta
  { id: 37, text: "Saya merasakan koneksi yang mendalam dengan orang-orang yang dekat dengan saya.", category: "Humanity", subcategory: "Cinta" },
  { id: 38, text: "Saya berada pada kondisi terbaik ketika saya berbagi hidup dengan orang lain.", category: "Humanity", subcategory: "Cinta" },
  { id: 39, text: "Mudah bagi saya untuk mengekspresikan cinta dan kehangatan kepada orang lain.", category: "Humanity", subcategory: "Cinta" },
  { id: 40, text: "Saya menghargai hubungan dekat saya di atas segalanya.", category: "Humanity", subcategory: "Cinta" },

  // Kemanusiaan (Humanity) - Kebaikan
  { id: 41, text: "Saya suka melakukan perbuatan baik untuk orang lain, bahkan jika mereka adalah orang asing.", category: "Humanity", subcategory: "Kebaikan" },
  { id: 42, text: "Saya adalah orang yang murah hati dan peduli.", category: "Humanity", subcategory: "Kebaikan" },
  { id: 43, text: "Membantu orang lain adalah salah satu hal terpenting bagi saya.", category: "Humanity", subcategory: "Kebaikan" },
  { id: 44, text: "Saya senang merawat orang-orang.", category: "Humanity", subcategory: "Kebaikan" },

  // Kemanusiaan (Humanity) - Kecerdasan Sosial
  { id: 45, text: "Saya terampil dalam memahami apa yang membuat orang lain bergerak.", category: "Humanity", subcategory: "Kecerdasan Sosial" },
  { id: 46, text: "Saya selalu sadar akan perasaan saya sendiri dan perasaan orang-orang di sekitar saya.", category: "Humanity", subcategory: "Kecerdasan Sosial" },
  { id: 47, text: "Saya tahu apa yang harus dilakukan untuk membuat orang lain merasa nyaman.", category: "Humanity", subcategory: "Kecerdasan Sosial" },
  { id: 48, text: "Saya dapat dengan mudah menyesuaikan perilaku saya untuk cocok dengan kelompok orang yang berbeda.", category: "Humanity", subcategory: "Kecerdasan Sosial" },

  // Note: This is a shortened version showing first 48 questions.
  // The full VIA assessment contains 96 questions (4 questions per character strength × 24 character strengths)
  // Additional questions for Justice, Temperance, and Transcendence categories would continue here...
];

// Assessment Configurations
export const assessmentTypes: AssessmentType[] = [
  {
    id: 'big-five',
    name: 'Big Five Personality',
    description: 'Inventori Lima Besar (BFI-44): Penilaian Diri',
    totalQuestions: 44,
    scaleType: '5-point',
    scaleLabels: [
      'Sangat tidak setuju',
      'Agak tidak setuju',
      'Netral (tidak setuju maupun tidak)',
      'Agak setuju',
      'Sangat setuju'
    ],
    questions: bigFiveQuestions
  },
  {
    id: 'riasec',
    name: 'RIASEC Holland Codes',
    description: 'Kode Holland RIASEC: Penilaian Diri',
    totalQuestions: 60,
    scaleType: '5-point',
    scaleLabels: [
      'Sangat tidak sesuai dengan saya',
      'Tidak sesuai dengan saya',
      'Netral',
      'Sesuai dengan saya',
      'Sangat sesuai dengan saya'
    ],
    questions: riasecQuestions
  },
  {
    id: 'via-character',
    name: 'VIA Character Strengths',
    description: 'Kekuatan Karakter VIA: Penilaian Diri',
    totalQuestions: 96,
    scaleType: '5-point',
    scaleLabels: [
      'Sangat tidak sesuai dengan saya',
      'Tidak sesuai dengan saya',
      'Netral',
      'Sesuai dengan saya',
      'Sangat sesuai dengan saya'
    ],
    questions: viaQuestions
  }
];

// Helper functions
export const getAssessmentById = (id: string): AssessmentType | undefined => {
  return assessmentTypes.find(assessment => assessment.id === id);
};

export const getQuestionsByCategory = (questions: Question[], category: string): Question[] => {
  return questions.filter(question => question.category === category);
};

export const getQuestionsBySubcategory = (questions: Question[], subcategory: string): Question[] => {
  return questions.filter(question => question.subcategory === subcategory);
};

// Scale configurations for different assessment types
export const scaleConfigurations = {
  '5-point': {
    values: [1, 2, 3, 4, 5],
    colors: ['#e53935', '#e53935', '#64707d', '#43a047', '#43a047']
  },
  '7-point': {
    values: [1, 2, 3, 4, 5, 6, 7],
    colors: ['#e53935', '#e53935', '#e53935', '#64707d', '#43a047', '#43a047', '#43a047']
  }
};
